import { useState } from "react"
import { Provider } from "react-redux"
import { PersistGate } from "@plasmohq/redux-persist/integration/react"
import { persistor, store } from "~store/store"
import { Button } from "./components/ui/button"
import "./style.css"

function OptionsIndex() {
  const [data, setData] = useState("")

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <div className="w-full max-w-3xl mx-auto p-6 bg-white">
          <h1 className="text-2xl font-bold mb-4">
            欢迎使用 <a href="https://www.plasmo.com" className="text-blue-500 hover:underline">Plasmo</a> 扩展!
          </h1>
          <h2 className="text-xl mb-6">这是选项页面!</h2>
          
          <div className="mb-6">
            <label htmlFor="data-input" className="block text-sm font-medium mb-2">输入数据:</label>
            <div className="flex gap-2">
              <input 
                id="data-input"
                onChange={(e) => setData(e.target.value)} 
                value={data} 
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入数据..."
              />
              <Button>保存</Button>
            </div>
          </div>
        </div>
      </PersistGate>
    </Provider>
  )
}

export default OptionsIndex